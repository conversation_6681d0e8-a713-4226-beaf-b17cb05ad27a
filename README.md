# SecOctopus & SecondOctopus

Two .NET 8.0 applications for creating secure tunnels between local and remote services.

## Overview

- **Sec<PERSON>ctopus**: Local tunnel client that exposes a local service (************:58000) to the internet via localtunnel
- **SecondOctopus**: Remote proxy that creates a local endpoint (localhost:61000) forwarding to the tunnel URL

## Prerequisites

- .NET 8.0 Runtime installed
- Internet connection for tunnel creation

## Usage

### Step 1: Start SecOctopus (Local Side)

Run the local tunnel client:
```bash
dotnet run --project SecOctopus/SecOctopus.csproj
```
Or use the batch file:
```bash
run-secoctopus.bat
```

This will:
1. Create a tunnel to expose your local service at ************:58000
2. Display a public URL (e.g., https://abc123.localtunnel.me)
3. Wait for connections

### Step 2: Start SecondOctopus (Remote Side)

Run the remote proxy:
```bash
dotnet run --project SecondOctopus/SecondOctopus.csproj
```
Or use the batch file:
```bash
run-secondoctopus.bat
```

This will:
1. Ask you to enter the tunnel URL from Step 1
2. Create a local proxy listening on localhost:61000
3. Forward all traffic to the tunnel URL

### Step 3: Connect Your Applications

Your applications can now connect to `https://localhost:61000` and the traffic will be forwarded through the tunnel to your local service at ************:58000.

## Architecture

```
[Local Service] ← SecOctopus ← [Internet/Tunnel] ← SecondOctopus ← [Your Apps]
************:58000              localtunnel.me              localhost:61000
```

## Building

To build both projects:
```bash
dotnet build SecOctopus.sln
```

## Features

- **Secure HTTPS tunneling** using localtunnel.me
- **Automatic reconnection** if connections are lost
- **Connection logging** for debugging
- **Certificate validation bypass** for tunnel connections
- **Cross-platform** .NET 8.0 support

## Troubleshooting

1. **Connection refused errors**: Make sure your local service is running on ************:58000
2. **Tunnel creation fails**: Check your internet connection
3. **Build errors**: Ensure .NET 8.0 SDK is installed

## Notes

- SecOctopus connects to the default localtunnel.me server
- SecondOctopus accepts any SSL certificate for tunnel connections (for development use)
- Both programs will display connection status and error messages
