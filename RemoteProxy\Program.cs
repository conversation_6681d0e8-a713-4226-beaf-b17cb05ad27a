using System.Net;
using System.Net.Sockets;
using System.Net.Security;
using System.Security.Cryptography.X509Certificates;

namespace RemoteProxy
{
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("Remote Proxy - Creating localhost:61000 endpoint");
            Console.WriteLine("===============================================");

            string tunnelUrl;
            while (true)
            {
                Console.Write("Enter the tunnel URL from local program: ");
                tunnelUrl = Console.ReadLine()?.Trim() ?? "";
                
                if (!string.IsNullOrEmpty(tunnelUrl) && Uri.TryCreate(tunnelUrl, UriKind.Absolute, out _))
                {
                    break;
                }
                
                Console.WriteLine("Please enter a valid URL.");
            }

            var uri = new Uri(tunnelUrl);
            var tunnelHost = uri.Host;
            var tunnelPort = uri.Port == -1 ? (uri.Scheme == "https" ? 443 : 80) : uri.Port;
            var isHttps = uri.Scheme == "https";

            var localPort = 61000;
            var listener = new TcpListener(IPAddress.Loopback, localPort);

            try
            {
                listener.Start();
                Console.WriteLine();
                Console.WriteLine($"✓ Remote proxy listening on localhost:{localPort}");
                Console.WriteLine($"✓ Forwarding to: {tunnelUrl}");
                Console.WriteLine($"✓ Protocol: {(isHttps ? "HTTPS" : "HTTP")}");
                Console.WriteLine();
                Console.WriteLine("Your applications can now connect to https://localhost:61000");
                Console.WriteLine("Press Ctrl+C to stop...");
                Console.WriteLine();

                var connectionCount = 0;
                while (true)
                {
                    var client = await listener.AcceptTcpClientAsync();
                    var connId = ++connectionCount;
                    Console.WriteLine($"[{connId}] New connection accepted");
                    
                    _ = Task.Run(async () => await HandleClientAsync(client, tunnelHost, tunnelPort, isHttps, connId));
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error starting proxy: {ex.Message}");
            }
            finally
            {
                listener?.Stop();
                Console.WriteLine("Proxy stopped.");
            }
        }

        static async Task HandleClientAsync(TcpClient client, string tunnelHost, int tunnelPort, bool isHttps, int connId)
        {
            try
            {
                using var clientStream = client.GetStream();
                using var tunnelClient = new TcpClient();
                
                Console.WriteLine($"[{connId}] Connecting to {tunnelHost}:{tunnelPort}...");
                await tunnelClient.ConnectAsync(tunnelHost, tunnelPort);
                
                Stream tunnelStream = tunnelClient.GetStream();
                
                if (isHttps)
                {
                    var sslStream = new SslStream(tunnelStream, false, ValidateServerCertificate);
                    await sslStream.AuthenticateAsClientAsync(tunnelHost);
                    tunnelStream = sslStream;
                }
                
                Console.WriteLine($"[{connId}] Connected, forwarding traffic...");
                
                var task1 = CopyStreamAsync(clientStream, tunnelStream, $"[{connId}] Client->Tunnel");
                var task2 = CopyStreamAsync(tunnelStream, clientStream, $"[{connId}] Tunnel->Client");

                await Task.WhenAny(task1, task2);
                Console.WriteLine($"[{connId}] Connection closed");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[{connId}] Connection error: {ex.Message}");
            }
            finally
            {
                try { client?.Close(); } catch { }
            }
        }

        static async Task CopyStreamAsync(Stream source, Stream destination, string label)
        {
            try
            {
                await source.CopyToAsync(destination);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"{label} copy error: {ex.Message}");
            }
        }

        static bool ValidateServerCertificate(object sender, X509Certificate? certificate, X509Chain? chain, SslPolicyErrors sslPolicyErrors)
        {
            return true; // Accept any certificate for tunnel connections
        }
    }
}
