﻿using Localtunnel;
using Localtunnel.Endpoints.Http;
using Localtunnel.Handlers.Kestrel;
using Localtunnel.Processors;
using Microsoft.Extensions.Logging;

namespace SecOctopus
{
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("SecOctopus - Local Tunnel Client");
            Console.WriteLine("===============================");
            Console.WriteLine("Exposing ************:58000 via secure tunnel");
            Console.WriteLine();

            // Choose subdomain
            string subdomain = ChooseSubdomain();

            Console.WriteLine($"Requesting subdomain: {subdomain}");
            Console.WriteLine("Note: If subdomain is taken, a random one will be assigned");
            Console.WriteLine();

            var loggerFactory = LoggerFactory.Create(x => x.AddConsole().SetMinimumLevel(LogLevel.Information));

            try
            {
                using var client = new LocaltunnelClient(loggerFactory);

                var pipeline = new HttpRequestProcessingPipelineBuilder()
                    .Append(new HttpHostHeaderRewritingRequestProcessor("************"))
                    .Build();

                var endpointFactory = new HttpsTunnelEndpointFactory("************", 58000);
                var tunnelConnectionHandler = new KestrelTunnelConnectionHandler(pipeline, endpointFactory);

                Console.WriteLine("Creating tunnel with custom subdomain...");
                var tunnel = await client
                    .OpenAsync(tunnelConnectionHandler, subdomain)
                    .ConfigureAwait(false);

                Console.WriteLine("Starting tunnel...");
                await tunnel
                    .StartAsync()
                    .ConfigureAwait(false);

                Console.WriteLine();
                Console.WriteLine($"✓ Tunnel created successfully!");
                Console.WriteLine($"✓ Requested subdomain: {subdomain}");
                Console.WriteLine($"✓ Actual URL: {tunnel.Information.Url}");
                Console.WriteLine($"✓ Forwarding to: https://************:58000");
                Console.WriteLine();
                Console.WriteLine("═══════════════════════════════════════════════════════════");
                Console.WriteLine("COPY THIS URL TO SecondOctopus:");
                Console.WriteLine($"{tunnel.Information.Url}");
                Console.WriteLine("═══════════════════════════════════════════════════════════");
                Console.WriteLine();
                Console.WriteLine("Press any key to stop the tunnel...");

                Console.ReadKey();

                Console.WriteLine("\nShutting down tunnel...");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
                Console.WriteLine("Press any key to exit...");
                Console.ReadKey();
            }
        }

        static string ChooseSubdomain()
        {
            Console.WriteLine("Choose your preferred subdomain:");
            Console.WriteLine("1. remote-secoc");
            Console.WriteLine("2. remote-zenzefi");
            Console.WriteLine("3. Random (let server choose)");
            Console.Write("Enter choice (1-3): ");

            var choice = Console.ReadLine();
            Console.WriteLine();

            return choice switch
            {
                "1" => "remote-secoc",
                "2" => "remote-zenzefi",
                "3" => null!, // null means random
                _ => "remote-secoc" // default to first option
            };
        }
    }
}