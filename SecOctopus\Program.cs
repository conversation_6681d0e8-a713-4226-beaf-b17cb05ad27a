﻿using Localtunnel;
using Localtunnel.Endpoints.Http;
using Localtunnel.Handlers.Kestrel;
using Localtunnel.Processors;
using Microsoft.Extensions.Logging;

namespace SecOctopus
{
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("Local Tunnel Client - Exposing ************:58000");
            Console.WriteLine("=====================================");

            var loggerFactory = LoggerFactory.Create(x => x.AddConsole().SetMinimumLevel(LogLevel.Information));

            try
            {
                using var client = new LocaltunnelClient(loggerFactory);

                var pipeline = new HttpRequestProcessingPipelineBuilder()
                    .Append(new HttpHostHeaderRewritingRequestProcessor("************"))
                    .Build();

                var endpointFactory = new HttpsTunnelEndpointFactory("************", 58000);
                var tunnelConnectionHandler = new KestrelTunnelConnectionHandler(pipeline, endpointFactory);

                Console.WriteLine("Creating tunnel...");
                var tunnel = await client
                    .OpenAsync(tunnelConnectionHandler)
                    .ConfigureAwait(false);

                Console.WriteLine("Starting tunnel...");
                await tunnel
                    .StartAsync()
                    .ConfigureAwait(false);

                Console.WriteLine();
                Console.WriteLine($"✓ Tunnel created successfully!");
                Console.WriteLine($"✓ Public URL: {tunnel.Information.Url}");
                Console.WriteLine($"✓ Forwarding to: https://************:58000");
                Console.WriteLine();
                Console.WriteLine("Copy the URL above and paste it into the remote program.");
                Console.WriteLine("Press any key to stop the tunnel...");

                Console.ReadKey();

                Console.WriteLine("\nShutting down tunnel...");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
                Console.WriteLine("Press any key to exit...");
                Console.ReadKey();
            }
        }
    }
}