﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net6.0</TargetFramework>
	  <RollForward>major</RollForward>
    <Nullable>enable</Nullable>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <PackAsTool>true</PackAsTool>
    <ToolCommandName>localtunnel</ToolCommandName>
    <Description>Localtunnel implementation in .NET</Description>
    <Company><PERSON></Company>
    <Version>2.0.0-preview.1</Version>
    <Copyright><PERSON> 2021</Copyright>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="5.0.0" />
    <PackageReference Include="System.CommandLine" Version="2.0.0-beta1.20574.7" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Localtunnel\Localtunnel.csproj" />
  </ItemGroup>

  <Target Name="Rename" AfterTargets="AfterBuild" Condition=" '$(BuildingInsideVisualStudio)' != 'true' ">
    <Move SourceFiles="$(OUTDIR)\$(MSBuildProjectName).exe" DestinationFiles="$(OUTDIR)\$(ToolCommandName).exe" />
    <Message Text="Renamed CLI executable to $(ToolCommandName)" Importance="high" />
  </Target>
</Project>
