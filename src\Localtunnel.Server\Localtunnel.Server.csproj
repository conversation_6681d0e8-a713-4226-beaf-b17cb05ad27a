<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
	<LangVersion>preview</LangVersion>
	<UserSecretsId>1d53e16c-c0be-49ec-b63a-2003cc1b028d</UserSecretsId>
	<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
	<DockerfileContext>..\..</DockerfileContext>
	<DockerfileTag>localtunnel-server</DockerfileTag>
  </PropertyGroup>

  <ItemGroup Condition=" Exists('appsettings.json') ">
    <Content Remove="appsettings.json" />
    <None Include="appsettings.json" >
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.14.0" />
  </ItemGroup>

</Project>
