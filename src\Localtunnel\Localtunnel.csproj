﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <Nullable>enable</Nullable>
	  <LangVersion>preview</LangVersion>
	  <OutputType>Library</OutputType>
	  <IsPackable>true</IsPackable>
    <Description>Localtunnel implementation in .NET</Description>
    <Company><PERSON></Company>
    <Version>2.0.0-preview.1</Version>
    <Copyright><PERSON> 2022</Copyright>
    <GeneratePackageOnBuild>True</GeneratePackageOnBuild>
    <Authors><PERSON></Authors>
    <PackageProjectUrl>https://github.com/angelobreuer/localtunnel.net</PackageProjectUrl>
    <RepositoryUrl>https://github.com/angelobreuer/localtunnel.net</RepositoryUrl>
    <RepositoryType>git</RepositoryType>
    <PackageTags>localtunnel</PackageTags>
    <NeutralLanguage>en-US</NeutralLanguage>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="6.0.0" />
    <PackageReference Include="System.Collections" Version="4.3.0" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Properties\Resources.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
</Project>
