﻿//------------------------------------------------------------------------------
// <auto-generated>
//     Dieser Code wurde von einem Tool generiert.
//     Laufzeitversion:4.0.30319.42000
//
//     Änderungen an dieser Datei können falsches Verhalten verursachen und gehen verloren, wenn
//     der Code erneut generiert wird.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Localtunnel.Properties {
    using System;
    
    
    /// <summary>
    ///   Eine stark typisierte Ressourcenklasse zum Suchen von lokalisierten Zeichenfolgen usw.
    /// </summary>
    // Diese Klasse wurde von der StronglyTypedResourceBuilder automatisch generiert
    // -Klasse über ein Tool wie ResGen oder Visual Studio automatisch generiert.
    // Um einen Member hinzuzufügen oder zu entfernen, bearbeiten Sie die .ResX-Datei und führen dann ResGen
    // mit der /str-Option erneut aus, oder Sie erstellen Ihr VS-Projekt neu.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Resources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resources() {
        }
        
        /// <summary>
        ///   Gibt die zwischengespeicherte ResourceManager-Instanz zurück, die von dieser Klasse verwendet wird.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Localtunnel.Properties.Resources", typeof(Resources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Überschreibt die CurrentUICulture-Eigenschaft des aktuellen Threads für alle
        ///   Ressourcenzuordnungen, die diese stark typisierte Ressourcenklasse verwenden.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Specifies whether untrusted certificates are allowed (use only for development/testing purposes). ähnelt.
        /// </summary>
        internal static string AllowUntrustedCertificatesDescription {
            get {
                return ResourceManager.GetString("AllowUntrustedCertificatesDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die If specified, opens the webpage in the browser. ähnelt.
        /// </summary>
        internal static string BrowserDescription {
            get {
                return ResourceManager.GetString("BrowserDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die [{Label}] Connection closed. ähnelt.
        /// </summary>
        internal static string ConnectionClosed {
            get {
                return ResourceManager.GetString("ConnectionClosed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die [{Label}] Connection error: {Error}, operation: {Operation}. ähnelt.
        /// </summary>
        internal static string ConnectionError {
            get {
                return ResourceManager.GetString("ConnectionError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Connections (E = establishing, C = closed/finished, O = open) ähnelt.
        /// </summary>
        internal static string ConnectionsHeader {
            get {
                return ResourceManager.GetString("ConnectionsHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Creating tunnel... ähnelt.
        /// </summary>
        internal static string CreatingTunnel {
            get {
                return ResourceManager.GetString("CreatingTunnel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Creating tunnel with {Connections} concurrent connection(s). ähnelt.
        /// </summary>
        internal static string CreatingTunnelWithNConnections {
            get {
                return ResourceManager.GetString("CreatingTunnelWithNConnections", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Current active connections ähnelt.
        /// </summary>
        internal static string CurrentActiveConnections {
            get {
                return ResourceManager.GetString("CurrentActiveConnections", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die day ähnelt.
        /// </summary>
        internal static string Day {
            get {
                return ResourceManager.GetString("Day", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die days ähnelt.
        /// </summary>
        internal static string Days {
            get {
                return ResourceManager.GetString("Days", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Unable to resolve DNS address for &apos;{0}&apos;. ähnelt.
        /// </summary>
        internal static string DnsResolutionFailed {
            get {
                return ResourceManager.GetString("DnsResolutionFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The host to proxy requests to. ähnelt.
        /// </summary>
        internal static string HostDescription {
            get {
                return ResourceManager.GetString("HostDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die hour ähnelt.
        /// </summary>
        internal static string Hour {
            get {
                return ResourceManager.GetString("Hour", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die hours ähnelt.
        /// </summary>
        internal static string Hours {
            get {
                return ResourceManager.GetString("Hours", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Starts a tunnel that exposes a HTTP server. ähnelt.
        /// </summary>
        internal static string HttpProxy {
            get {
                return ResourceManager.GetString("HttpProxy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Starts a tunnel that exposes a HTTPS server. ähnelt.
        /// </summary>
        internal static string HttpsProxy {
            get {
                return ResourceManager.GetString("HttpsProxy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die in ähnelt.
        /// </summary>
        internal static string In {
            get {
                return ResourceManager.GetString("In", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Max concurrent connections ähnelt.
        /// </summary>
        internal static string MaxConcurrentConnections {
            get {
                return ResourceManager.GetString("MaxConcurrentConnections", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The number of maximum allowed connections. ähnelt.
        /// </summary>
        internal static string MaximumConnectionsDescription {
            get {
                return ResourceManager.GetString("MaximumConnectionsDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die minute ähnelt.
        /// </summary>
        internal static string Minute {
            get {
                return ResourceManager.GetString("Minute", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die minutes ähnelt.
        /// </summary>
        internal static string Minutes {
            get {
                return ResourceManager.GetString("Minutes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die If specified, disables the dashboard. ähnelt.
        /// </summary>
        internal static string NoDashboardDescription {
            get {
                return ResourceManager.GetString("NoDashboardDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Onlice since ähnelt.
        /// </summary>
        internal static string OnlineSince {
            get {
                return ResourceManager.GetString("OnlineSince", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die out ähnelt.
        /// </summary>
        internal static string Out {
            get {
                return ResourceManager.GetString("Out", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die If specified, the request is proxied as received and no HTTP headers are reinterpreted. ähnelt.
        /// </summary>
        internal static string PassthroughDescription {
            get {
                return ResourceManager.GetString("PassthroughDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Port ähnelt.
        /// </summary>
        internal static string Port {
            get {
                return ResourceManager.GetString("Port", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The port to proxy requests to. ähnelt.
        /// </summary>
        internal static string PortDescription {
            get {
                return ResourceManager.GetString("PortDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Press [Ctrl] + [C] to exit. ähnelt.
        /// </summary>
        internal static string PressToExit {
            get {
                return ResourceManager.GetString("PressToExit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The minimum number of bytes to use for the receive buffer. ähnelt.
        /// </summary>
        internal static string ReceiveBufferSizeDescription {
            get {
                return ResourceManager.GetString("ReceiveBufferSizeDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die second ähnelt.
        /// </summary>
        internal static string Second {
            get {
                return ResourceManager.GetString("Second", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die seconds ähnelt.
        /// </summary>
        internal static string Seconds {
            get {
                return ResourceManager.GetString("Seconds", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Sending HTTP GET request ({URI})... ähnelt.
        /// </summary>
        internal static string SendingHttpGet {
            get {
                return ResourceManager.GetString("SendingHttpGet", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The hostname of the server to use. ähnelt.
        /// </summary>
        internal static string ServerDescription {
            get {
                return ResourceManager.GetString("ServerDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Shutting down... ähnelt.
        /// </summary>
        internal static string ShuttingDown {
            get {
                return ResourceManager.GetString("ShuttingDown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Starting browser at {Url}... ähnelt.
        /// </summary>
        internal static string StartingBrowser {
            get {
                return ResourceManager.GetString("StartingBrowser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The name of the subdomain to use, if not specified a random subdomain name is used. ähnelt.
        /// </summary>
        internal static string SubdomainDescription {
            get {
                return ResourceManager.GetString("SubdomainDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Tunnel {Id} created (max connections: {MaxConnections}, Port: {Port}, URI: {URI}) ähnelt.
        /// </summary>
        internal static string TunnelCreated {
            get {
                return ResourceManager.GetString("TunnelCreated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Tunnel Id ähnelt.
        /// </summary>
        internal static string TunnelId {
            get {
                return ResourceManager.GetString("TunnelId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die (tunnel offline) ähnelt.
        /// </summary>
        internal static string TunnelOffline {
            get {
                return ResourceManager.GetString("TunnelOffline", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die tunnel online @ tunnelclient ähnelt.
        /// </summary>
        internal static string TunnelOnline {
            get {
                return ResourceManager.GetString("TunnelOnline", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die URI ähnelt.
        /// </summary>
        internal static string TunnelURI {
            get {
                return ResourceManager.GetString("TunnelURI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Enables detailed verbose output. ähnelt.
        /// </summary>
        internal static string VerboseDescription {
            get {
                return ResourceManager.GetString("VerboseDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Waiting for request... ähnelt.
        /// </summary>
        internal static string WaitingForRequest {
            get {
                return ResourceManager.GetString("WaitingForRequest", resourceCulture);
            }
        }
    }
}
