﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AllowUntrustedCertificatesDescription" xml:space="preserve">
    <value>Gibt an, ob nicht vertrauenswürdige Zertifikate akzeptiert werden sollen.</value>
  </data>
  <data name="BrowserDescription" xml:space="preserve">
    <value>Wenn angegeben, wird die Website zu der der Tunnel hinführt, im Browser geöffnet.</value>
  </data>
  <data name="ConnectionClosed" xml:space="preserve">
    <value>[{Label}] Verbindung geschlossen.</value>
  </data>
  <data name="ConnectionError" xml:space="preserve">
    <value>[{Label}] Verbindungsfehler: {Error} während einer {Operation}-Anfrage.</value>
  </data>
  <data name="CreatingTunnel" xml:space="preserve">
    <value>Erstelle Tunnel...</value>
  </data>
  <data name="DnsResolutionFailed" xml:space="preserve">
    <value>Bei der Auflösung einer DNS-Adresse für '{0}' ist ein schwerwiegender Fehler aufgetreten.</value>
  </data>
  <data name="HostDescription" xml:space="preserve">
    <value>Der Host, zu dem der Tunnel eine Proxy erstellen soll.</value>
  </data>
  <data name="PortDescription" xml:space="preserve">
    <value>Der Port, zu dem der Tunnel eine Proxy erstellen soll.</value>
  </data>
  <data name="HttpProxy" xml:space="preserve">
    <value>Startet einen Tunnel für einen HTTP-Server.</value>
  </data>
  <data name="HttpsProxy" xml:space="preserve">
    <value>Startet einen Tunnel für einen HTTPS-Server.</value>
  </data>
  <data name="MaximumConnectionsDescription" xml:space="preserve">
    <value>Die Anzahl der maximal erlaubten gleichzeitigen Verbindungen.</value>
  </data>
  <data name="PassthroughDescription" xml:space="preserve">
    <value>Falls angegeben, werden die HTTP-Header so an den Server weitergegeben ohne diese zu verarbeiten und anzupassen.</value>
  </data>
  <data name="ReceiveBufferSizeDescription" xml:space="preserve">
    <value>Die Anzahl der Bytes die für den Receive-Buffer allokalisiert werden sollen.</value>
  </data>
  <data name="SendingHttpGet" xml:space="preserve">
    <value>Sende HTTP GET Anfrage ({URI})...</value>
  </data>
  <data name="ServerDescription" xml:space="preserve">
    <value>Der DNS-Hostname oder die IP-Adresse des Tunnelservers.</value>
  </data>
  <data name="SubdomainDescription" xml:space="preserve">
    <value>Der Name der Subdomäne, sonst wird ein zufälliger Subdomänenname generiert.</value>
  </data>
  <data name="TunnelCreated" xml:space="preserve">
    <value>Tunnel {Id} erstellt (maximale gleichzeitige Verbindungen: {MaxConnection}, Port: {Port}, URI: {URI})</value>
  </data>
  <data name="VerboseDescription" xml:space="preserve">
    <value>Aktiviert die detaillierte Ausgabe.</value>
  </data>
  <data name="TunnelOnline" xml:space="preserve">
    <value>Tunnel aktiv @ tunnelclient</value>
  </data>
  <data name="TunnelOffline" xml:space="preserve">
    <value>(Tunnel offline)</value>
  </data>
  <data name="Day" xml:space="preserve">
    <value>Tag</value>
  </data>
  <data name="Days" xml:space="preserve">
    <value>Tage</value>
  </data>
  <data name="Hour" xml:space="preserve">
    <value>Stunde</value>
  </data>
  <data name="Hours" xml:space="preserve">
    <value>Stunden</value>
  </data>
  <data name="Minute" xml:space="preserve">
    <value>Minute</value>
  </data>
  <data name="Minutes" xml:space="preserve">
    <value>Minuten</value>
  </data>
  <data name="Second" xml:space="preserve">
    <value>Sekunde</value>
  </data>
  <data name="Seconds" xml:space="preserve">
    <value>Sekunden</value>
  </data>
  <data name="TunnelId" xml:space="preserve">
    <value>Tunnel Id</value>
  </data>
  <data name="TunnelURI" xml:space="preserve">
    <value>URI</value>
  </data>
  <data name="OnlineSince" xml:space="preserve">
    <value>Aktiv seit</value>
  </data>
  <data name="Port" xml:space="preserve">
    <value>Port</value>
  </data>
  <data name="MaxConcurrentConnections" xml:space="preserve">
    <value>Max. gleichzeitige Verbindungen</value>
  </data>
  <data name="CurrentActiveConnections" xml:space="preserve">
    <value>Aktive Verbindungen</value>
  </data>
  <data name="CreatingTunnelWithNConnections" xml:space="preserve">
    <value>Erstelle Tunnel mit maximal {Connections} gleichzeitige Verbindungen.</value>
  </data>
  <data name="StartingBrowser" xml:space="preserve">
    <value>Starte Browser '{Url}'...</value>
  </data>
  <data name="PressToExit" xml:space="preserve">
    <value>Drücke [Strg] + [C] zum Beenden.</value>
  </data>
  <data name="ShuttingDown" xml:space="preserve">
    <value>Stoppe Tunnel...</value>
  </data>
  <data name="WaitingForRequest" xml:space="preserve">
    <value>Warte auf HTTP-Anfrage...</value>
  </data>
  <data name="Out" xml:space="preserve">
    <value>ausgehend</value>
  </data>
  <data name="In" xml:space="preserve">
    <value>eingehend</value>
  </data>
  <data name="ConnectionsHeader" xml:space="preserve">
    <value>Verbindungen (E = Verbindungsherstellung, C = geschlossen, O = offen)</value>
  </data>
  <data name="NoDashboardDescription" xml:space="preserve">
    <value>Wenn angegeben, wird das Dashboard deaktiviert.</value>
  </data>
</root>