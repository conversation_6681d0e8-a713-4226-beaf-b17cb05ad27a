﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AllowUntrustedCertificatesDescription" xml:space="preserve">
    <value>Specifies whether untrusted certificates are allowed (use only for development/testing purposes).</value>
  </data>
  <data name="BrowserDescription" xml:space="preserve">
    <value>If specified, opens the webpage in the browser.</value>
  </data>
  <data name="ConnectionClosed" xml:space="preserve">
    <value>[{Label}] Connection closed.</value>
  </data>
  <data name="ConnectionError" xml:space="preserve">
    <value>[{Label}] Connection error: {Error}, operation: {Operation}.</value>
  </data>
  <data name="CreatingTunnel" xml:space="preserve">
    <value>Creating tunnel...</value>
  </data>
  <data name="DnsResolutionFailed" xml:space="preserve">
    <value>Unable to resolve DNS address for '{0}'.</value>
  </data>
  <data name="HostDescription" xml:space="preserve">
    <value>The host to proxy requests to.</value>
  </data>
  <data name="HttpProxy" xml:space="preserve">
    <value>Starts a tunnel that exposes a HTTP server.</value>
  </data>
  <data name="HttpsProxy" xml:space="preserve">
    <value>Starts a tunnel that exposes a HTTPS server.</value>
  </data>
  <data name="MaximumConnectionsDescription" xml:space="preserve">
    <value>The number of maximum allowed connections.</value>
  </data>
  <data name="PassthroughDescription" xml:space="preserve">
    <value>If specified, the request is proxied as received and no HTTP headers are reinterpreted.</value>
  </data>
  <data name="PortDescription" xml:space="preserve">
    <value>The port to proxy requests to.</value>
  </data>
  <data name="ReceiveBufferSizeDescription" xml:space="preserve">
    <value>The minimum number of bytes to use for the receive buffer.</value>
  </data>
  <data name="SendingHttpGet" xml:space="preserve">
    <value>Sending HTTP GET request ({URI})...</value>
  </data>
  <data name="ServerDescription" xml:space="preserve">
    <value>The hostname of the server to use.</value>
  </data>
  <data name="SubdomainDescription" xml:space="preserve">
    <value>The name of the subdomain to use, if not specified a random subdomain name is used.</value>
  </data>
  <data name="TunnelCreated" xml:space="preserve">
    <value>Tunnel {Id} created (max connections: {MaxConnections}, Port: {Port}, URI: {URI})</value>
  </data>
  <data name="VerboseDescription" xml:space="preserve">
    <value>Enables detailed verbose output.</value>
  </data>
  <data name="TunnelOnline" xml:space="preserve">
    <value>tunnel online @ tunnelclient</value>
  </data>
  <data name="TunnelOffline" xml:space="preserve">
    <value>(tunnel offline)</value>
  </data>
  <data name="Hour" xml:space="preserve">
    <value>hour</value>
  </data>
  <data name="Hours" xml:space="preserve">
    <value>hours</value>
  </data>
  <data name="Minute" xml:space="preserve">
    <value>minute</value>
  </data>
  <data name="Minutes" xml:space="preserve">
    <value>minutes</value>
  </data>
  <data name="Day" xml:space="preserve">
    <value>day</value>
  </data>
  <data name="Days" xml:space="preserve">
    <value>days</value>
  </data>
  <data name="Second" xml:space="preserve">
    <value>second</value>
  </data>
  <data name="Seconds" xml:space="preserve">
    <value>seconds</value>
  </data>
  <data name="TunnelId" xml:space="preserve">
    <value>Tunnel Id</value>
  </data>
  <data name="TunnelURI" xml:space="preserve">
    <value>URI</value>
  </data>
  <data name="OnlineSince" xml:space="preserve">
    <value>Onlice since</value>
  </data>
  <data name="Port" xml:space="preserve">
    <value>Port</value>
  </data>
  <data name="MaxConcurrentConnections" xml:space="preserve">
    <value>Max concurrent connections</value>
  </data>
  <data name="CurrentActiveConnections" xml:space="preserve">
    <value>Current active connections</value>
  </data>
  <data name="CreatingTunnelWithNConnections" xml:space="preserve">
    <value>Creating tunnel with {Connections} concurrent connection(s).</value>
  </data>
  <data name="StartingBrowser" xml:space="preserve">
    <value>Starting browser at {Url}...</value>
  </data>
  <data name="PressToExit" xml:space="preserve">
    <value>Press [Ctrl] + [C] to exit.</value>
  </data>
  <data name="ShuttingDown" xml:space="preserve">
    <value>Shutting down...</value>
  </data>
  <data name="WaitingForRequest" xml:space="preserve">
    <value>Waiting for request...</value>
  </data>
  <data name="Out" xml:space="preserve">
    <value>out</value>
  </data>
  <data name="In" xml:space="preserve">
    <value>in</value>
  </data>
  <data name="ConnectionsHeader" xml:space="preserve">
    <value>Connections (E = establishing, C = closed/finished, O = open)</value>
  </data>
  <data name="NoDashboardDescription" xml:space="preserve">
    <value>If specified, disables the dashboard.</value>
  </data>
</root>