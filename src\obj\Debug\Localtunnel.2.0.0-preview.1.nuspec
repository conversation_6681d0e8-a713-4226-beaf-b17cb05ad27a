﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>Localtunnel</id>
    <version>2.0.0-preview.1</version>
    <authors><PERSON></authors>
    <projectUrl>https://github.com/angelobreuer/localtunnel.net</projectUrl>
    <description>Localtunnel implementation in .NET</description>
    <copyright><PERSON> 2022</copyright>
    <tags>localtunnel</tags>
    <repository type="git" url="https://github.com/angelobreuer/localtunnel.net" />
    <dependencies>
      <group targetFramework="net8.0">
        <dependency id="Microsoft.Extensions.Logging.Abstractions" version="8.0.0" exclude="Build,Analyzers" />
        <dependency id="System.Collections" version="4.3.0" exclude="Build,Analyzers" />
      </group>
    </dependencies>
    <frameworkReferences>
      <group targetFramework="net8.0">
        <frameworkReference name="Microsoft.AspNetCore.App" />
      </group>
    </frameworkReferences>
  </metadata>
  <files>
    <file src="C:\Users\<USER>\source\repos\SecOctopus\src\bin\Debug\net8.0\de-DE\Localtunnel.resources.dll" target="lib\net8.0\de-DE\Localtunnel.resources.dll" />
    <file src="C:\Users\<USER>\source\repos\SecOctopus\src\bin\Debug\net8.0\Localtunnel.dll" target="lib\net8.0\Localtunnel.dll" />
  </files>
</package>