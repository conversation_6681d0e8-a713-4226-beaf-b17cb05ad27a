//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Reflection;

[assembly: System.Reflection.AssemblyCompanyAttribute("Angelo Breuer")]
[assembly: System.Reflection.AssemblyConfigurationAttribute("Debug")]
[assembly: System.Reflection.AssemblyCopyrightAttribute("<PERSON> Breuer 2022")]
[assembly: System.Reflection.AssemblyDescriptionAttribute("Localtunnel implementation in .NET")]
[assembly: System.Reflection.AssemblyFileVersionAttribute("*******")]
[assembly: System.Reflection.AssemblyInformationalVersionAttribute("2.0.0-preview.1")]
[assembly: System.Reflection.AssemblyProductAttribute("Localtunnel")]
[assembly: System.Reflection.AssemblyTitleAttribute("Localtunnel")]
[assembly: System.Reflection.AssemblyVersionAttribute("*******")]
[assembly: System.Reflection.AssemblyMetadataAttribute("RepositoryUrl", "https://github.com/angelobreuer/localtunnel.net")]
[assembly: System.Resources.NeutralResourcesLanguageAttribute("en-US")]

// Generated by the MSBuild WriteCodeFragment class.

